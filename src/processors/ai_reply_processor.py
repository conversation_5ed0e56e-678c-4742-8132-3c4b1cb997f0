"""
AI回复处理器 - 使用AI生成邮件回复内容并发送
"""

import os
import sys
from typing import Dict, Any, Optional
from loguru import logger

# 添加项目根目录到sys.path
base_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(base_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.processor_manager import BaseProcessor
from src.core.ai_analyzer import AnalysisResult
from src.utils.smtp_sender import send_email

class AIReplyProcessor(BaseProcessor):
    """AI回复处理器 - 使用AI生成邮件回复并发送"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.enabled = config.get('enabled', False) if config else False
        self.ai_prompt = config.get('ai_prompt', "请根据以下邮件内容生成合适的回复：") if config else "请根据以下邮件内容生成合适的回复："
        
    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """判断是否需要AI回复"""
        if not self.enabled:
            return False
            
        # 检查是否需要行动
        if not analysis.action_required:
            return False
            
        # 避免回复自动生成的邮件
        subject = email_data.get('subject', '').lower()
        if any(keyword in subject for keyword in ['re:', 'fwd:', 'auto-reply', 'out of office']):
            return False
            
        return True
    
    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """使用AI生成回复并发送"""
        try:
            # 构建AI提示
            prompt = self._build_ai_prompt(email_data)
            
            # 在实际应用中，这里调用AI服务生成回复
            # 示例中使用固定回复，实际应替换为AI调用
            ai_reply = self._call_ai_service(prompt)  # 实际实现中应调用真正的AI服务
            
            # 发送回复邮件
            self._send_reply_email(
                recipient=email_data.get('sender', ''),
                subject=f"Re: {email_data.get('subject', '')}",
                content=ai_reply
            )
            
            return {
                "reply_sent": True,
                "recipient": email_data.get('sender', ''),
                "ai_reply": ai_reply
            }
            
        except Exception as e:
            logger.error(f"AI回复处理失败: {e}")
            return {
                "reply_sent": False,
                "error": str(e)
            }
    
    def _build_ai_prompt(self, email_data: Dict[str, Any]) -> str:
        """构建AI提示词"""
        return f"""
        {self.ai_prompt}
        
        邮件主题: {email_data.get('subject', '')}
        发件人: {email_data.get('sender', '')}
        邮件内容:
        {email_data.get('body', '')}
        """
    
    def _call_ai_service(self, prompt: str) -> str:
        """调用AI服务生成回复"""
        try:
            # 导入AI分析器配置
            from src.core.config_manager import ConfigManager
            from src.core.ai_analyzer import AIAnalyzer

            # 获取配置
            config_manager = ConfigManager()
            config = config_manager.load_config()

            # 创建AI分析器实例
            ai_analyzer = AIAnalyzer(config.ai)

            # 构建回复生成提示
            reply_prompt = f"""
            请根据以下邮件内容生成一个专业、友好的回复邮件：

            {prompt}

            回复要求：
            1. 语气要专业且友好
            2. 确认收到邮件
            3. 根据邮件内容提供有用的回复
            4. 如果需要进一步沟通，请提及
            5. 回复长度适中，不要过长

            请直接返回回复内容，不需要包含主题行或签名。
            """

            # 根据AI提供商类型调用相应的API
            provider = ai_analyzer.provider
            client = getattr(provider, 'client', None)
            if client:
                if ai_analyzer.config.provider.lower() == "openai":
                    # OpenAI API调用
                    response = client.chat.completions.create(
                        model=ai_analyzer.config.model,
                        messages=[
                            {"role": "system", "content": "你是一个专业的邮件助手，负责生成合适的邮件回复。"},
                            {"role": "user", "content": reply_prompt}
                        ],
                        max_tokens=500,
                        temperature=0.7
                    )
                    reply_content = response.choices[0].message.content
                elif ai_analyzer.config.provider.lower() == "anthropic":
                    # Anthropic API调用
                    response = client.messages.create(
                        model=ai_analyzer.config.model,
                        max_tokens=500,
                        temperature=0.7,
                        messages=[
                            {"role": "user", "content": reply_prompt}
                        ]
                    )
                    # 处理Anthropic响应格式
                    if hasattr(response, 'content') and isinstance(response.content, list) and len(response.content) > 0:
                        reply_content = getattr(response.content[0], 'text', None)
                    else:
                        reply_content = str(response.content)
                else:
                    reply_content = None

                if reply_content:
                    return reply_content.strip()
                else:
                    return "感谢您的邮件！我已收到您的消息，会尽快处理并回复您。"
            else:
                return "感谢您的邮件！我已收到您的消息，会尽快处理并回复您。"

        except Exception as e:
            logger.error(f"AI回复生成失败: {e}")
            # 如果AI服务失败，返回默认回复
            return "感谢您的邮件！我已收到您的消息，会尽快处理并回复您。"
    
    def _send_reply_email(self, recipient: str, subject: str, content: str):
        """发送回复邮件"""
        logger.info(f"准备发送AI回复给: {recipient}")
        logger.info(f"主题: {subject}")
        logger.info(f"内容: {content}")

        # 使用SMTP发送邮件
        try:
            success = send_email(
                to=recipient,
                subject=subject,
                body=content
            )

            if success:
                logger.info(f"AI回复邮件发送成功: {recipient}")
            else:
                logger.error(f"AI回复邮件发送失败: {recipient}")

        except Exception as e:
            logger.error(f"发送AI回复邮件时出错: {e}")
            # 如果SMTP发送失败，记录详细信息但不中断处理
            logger.info(f"邮件发送失败，但已记录处理日志")
    
    def get_priority(self) -> int:
        """AI回复处理器优先级较高"""
        return 30
    
    def validate_config(self) -> bool:
        """验证配置"""
        return True